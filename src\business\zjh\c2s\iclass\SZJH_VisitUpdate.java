package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.room.RoomPosInfo;
import jsproto.c2s.iclass.room.SBase_VisitUpdate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**	
 * 位置更新通知	
 *	
 * <AUTHOR>	
 */	
@SuppressWarnings("serial")
@Data
@EqualsAndHashCode(callSuper = true)
public class SZJH_VisitUpdate extends SBase_VisitUpdate {

    public static SZJH_VisitUpdate make(long roomID, int action, int pos, RoomPosInfo posInfo, int visit, RoomPosInfo visitInfo) {
        SZJH_VisitUpdate ret = new SZJH_VisitUpdate();
        ret.setRoomID(roomID);
        ret.setAction(action);
        ret.setPos(pos);
        ret.setVisit(visit);
        ret.setPosInfo(posInfo);
        ret.setVisitInfo(visitInfo);
        return ret;
    }

}	

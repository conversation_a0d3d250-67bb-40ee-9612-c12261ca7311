package business.zjh.c2s.iclass;

import business.zjh.c2s.cclass.ZJHRoomSetInfo;
import core.db.persistence.BaseDao;
import jsproto.c2s.cclass.*;

/**
 * 一局游戏开始
 *
 * <AUTHOR>
 */
public class SZJH_SetStart extends BaseSendMsg {

    public long roomID;
    public ZJHRoomSetInfo setInfo;

    public static SZJH_SetStart make(long roomID, ZJHRoomSetInfo setInfo) {
        SZJH_SetStart ret = new SZJH_SetStart();
        ret.roomID = roomID;
        ret.setInfo = setInfo;
        return ret;
    }
}

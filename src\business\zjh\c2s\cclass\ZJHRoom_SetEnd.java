package business.zjh.c2s.cclass;

import business.global.pk.zjh.ZJHRoomPos;
import jsproto.c2s.cclass.room.RoomSetEndInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 牛牛 配置
 * <AUTHOR>
 *
 */

	
// 一局结束的信息
public class ZJHRoom_SetEnd extends RoomSetEndInfo {
	public int endTime = 0;
//	public ArenaRoomCfg aRoomCfg;
	public List<ZJHRoom_PosEnd> posResultList = new ArrayList<>(); // 每个玩家的结算
}
	

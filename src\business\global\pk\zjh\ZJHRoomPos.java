package business.global.pk.zjh;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import business.global.room.base.AbsBaseRoom;
import business.global.room.base.AbsRoomPos;
import business.zjh.c2s.cclass.ZJHRoom_PosEnd;
import business.zjh.c2s.cclass.ZJH_define;
import cenum.RoomTypeEnum;
import cenum.room.RoomState;
import cenum.room.SetState;
import com.ddm.server.common.utils.BeanUtils;
import com.ddm.server.common.utils.CommMath;
import com.ddm.server.common.utils.CommTime;
import jsproto.c2s.iclass.room.VisitUpdateEnum;
import org.apache.commons.collections.MapUtils;

public class ZJHRoomPos extends AbsRoomPos {

    public ArrayList<Integer> privateCards = new ArrayList<>(); // 手牌
    private int m_nWin = 0; // 赢场数
    private int m_nLose = 0; // 输场数
    private int m_nFlat = 0; // 平场数
    private ArrayList<Boolean> m_TuiZhuList;//上一句是否推注
    private boolean insufficientPointsTrusteeship = false; // 标记是否因为积分不足而托管

    public ZJHRoomPos(int posID, AbsBaseRoom room) {
        super(posID, room);
        m_TuiZhuList = new ArrayList<Boolean>(Collections.nCopies(room.getCount(), false));
    }

    public ZJHRoomPos(int posID, boolean isVisitor, AbsBaseRoom room) {
        super(posID, isVisitor, room);
        m_TuiZhuList = new ArrayList<Boolean>(Collections.nCopies(room.getCount(), false));
    }

    /**
     * 初始化手牌
     *
     * @param cards
     */
    public void init(List<Integer> cards) {
        this.privateCards = new ArrayList<>(cards);
    }

    /**
     * 作弊初始化
     * 暂时没用到。。。先留着
     *
     * @param publicCards
     * @param handCard
     */
    public void init(List<Integer> cards, List<List<Integer>> publicCards, int handCard) {
        this.privateCards = new ArrayList<>(cards);
    }

    @Override
    public AbsRoomPos getNewPos(){
        return new ZJHRoomPos(this.getPosID(), this.isVisitor(), this.getRoom());
    }


    /**
     * 获取牌组信息
     *
     * @return
     */
    public ArrayList<Integer> getNotifyCard(long pid) {
        boolean isSelf = pid == this.getPid();

        ZJHRoomSet roomSet = (ZJHRoomSet) this.getRoom().getCurSet();
        if (null != roomSet && roomSet.state == SetState.End) {
            isSelf = true;
        }

        ArrayList<Integer> sArrayList = new ArrayList<Integer>();
        // 是自己
        int length = privateCards.size();
        for (int i = 0; i < length; i++) {
            sArrayList.add(isSelf ? privateCards.get(i) : Integer.valueOf((Integer) 0x00));
        }
        return sArrayList;
    }


    public ZJHRoom_PosEnd calcPosEnd() {
        ZJHRoomSet set = (ZJHRoomSet) this.getRoom().getCurSet();
//        this.setPoint(this.getPoint() + set.pointList.get(this.getPosID()));
        //移动到外面 panvc
//        set.pointList.set(this.getPosID(), set.addScoreList.get(this.getPosID()));
        this.calcRoomPoint(set.pointList.get(this.getPosID()));

        ZJHRoom_PosEnd posEnd = new ZJHRoom_PosEnd();
        int setPoint = set.pointList.get(this.getPosID());
        posEnd.addBet = set.addScoreList.get(this.getPosID());
        posEnd.cardList = this.privateCards;
        posEnd.point = set.pointList.get(this.getPosID());
        posEnd.pos = this.getPosID();
        posEnd.pid = this.getPid();
        posEnd.sex = this.getSex();
        posEnd.name = this.getName();
        posEnd.headImageUrl = this.getHeadImageUrl();
        posEnd.isPlaying = set.playingList.get(this.getPosID());
        posEnd.isCallBacker = set.getBackerPos() == this.getPosID() ? true : false;
        if (posEnd.isCallBacker) posEnd.callBackerNum = set.getCallbackerNum(this.getPosID());
        posEnd.crawType = set.crawTypeList.get(this.getPosID());
        posEnd.sportsPoint = setSportsPoint(setPoint); //设置竞技点  panvc

//        set.pointList.set(this.getPosID(), set.pointList.get(this.getPosID()) + this.getPoint());
//        posEnd.point = getPoint();
        return posEnd;
    }

    /**
     * @return privateCards
     */
    public ArrayList<Integer> getPrivateCards() {
        return privateCards;
    }

    /**
     * @return m_nWin
     */
    public int getWin() {
        return m_nWin;
    }

    /**
     *
     */
    public void addWin(int nWin) {
        this.m_nWin += nWin;
    }

    /**
     * @return m_nLose
     */
    public int getLose() {
        return m_nLose;
    }

    /**
     *
     */
    public void addLose(int nLose) {
        this.m_nLose += nLose;
    }

    /**
     * @return m_nFlat
     */
    public int getFlat() {
        return m_nFlat;
    }

    /**
     *
     */
    public void addFlat(int nFlat) {
        this.m_nFlat += nFlat;
    }

    /**
     * @return m_bTuiZhu
     */
    public boolean isTuiZhu(int curID) {
        boolean flag = false;
        if (curID < this.m_TuiZhuList.size() && curID >= 0) {
            flag = this.m_TuiZhuList.get(curID);
        }
        return flag;
    }

    /**
     *
     */
    public void setTuiZhu(int pos, boolean bTuiZhu) {
        this.m_TuiZhuList.set(pos, bTuiZhu);
    }

    @Override
    public Double setSportsPoint(int value) {
        if ((getRoom().calcFenUseYiKao()||getRoom().isRulesOfCanNotBelowZero()) && RoomTypeEnum.UNION.equals(this.getRoom().getRoomTypeEnum())) {
            if (RoomTypeEnum.UNION.equals(this.getRoom().getRoomTypeEnum())) {
                ZJHRoomSet set = (ZJHRoomSet) this.getRoom().getCurSet();
                if(Objects.nonNull(set)){
                    return CommMath.FormatDouble(set.yiKaoPointList.get(this.getPosID()));
                }
            } else {
                return null;
            }
        }
        return super.setSportsPoint(value);
    }

    @Override
    public Double sportsPoint() {
        if ((getRoom().calcFenUseYiKao()||getRoom().isRulesOfCanNotBelowZero()) && RoomTypeEnum.UNION.equals(this.getRoom().getRoomTypeEnum())) {
            if (RoomTypeEnum.UNION.equals(this.getRoom().getRoomTypeEnum())) {
                return CommMath.FormatDouble(this.getPointYiKao());
            } else {
                return null;
            }
        }
        return super.sportsPoint();
    }

    /**
     * 计算房间分数 panvc
     */
    public void calcRoomPoint(int point) {
        ZJHRoomSet set = (ZJHRoomSet) this.getRoom().getCurSet();
        if(set==null || !RoomTypeEnum.UNION.equals(this.getRoom().getRoomTypeEnum())){
            //防止当前局出现
            super.calcRoomPoint(point);
            return;
        }
        Double deductPointYiKao = set.yiKaoPointList.get(this.getPosID());
        this.setPoint(this.getPoint() + point);
        this.setPointYiKao(CommMath.addDouble(this.getPointYiKao(), deductPointYiKao));
        this.calcRoomSportsPoint(point, deductPointYiKao);
        if (getRoom().isRulesOfCanNotBelowZero() && RoomTypeEnum.UNION.equals(this.getRoom().getRoomTypeEnum())) {
            //小于0的 直接重置0
            if (this.getRoomSportsPoint() <= 0) {
                this.setRoomSportsPoint(0D);
            }
        }
    }
    public void clearKickOut(){
        this.privateCards.clear();
        this.m_nWin = 0; // 赢场数
        this.m_nLose = 0; // 输场数
        this.m_nFlat = 0; // 平场数
    }

    /**
     * 设置是否因积分不足而托管
     * @param insufficientPointsTrusteeship 是否因积分不足而托管
     */
    public void setInsufficientPointsTrusteeship(boolean insufficientPointsTrusteeship) {
        this.insufficientPointsTrusteeship = insufficientPointsTrusteeship;
        if (insufficientPointsTrusteeship) {
            setTrusteeship(true);
        } else if (this.insufficientPointsTrusteeship && !insufficientPointsTrusteeship) {
            setTrusteeship(false);
        }
    }
    
    /**
     * 是否因积分不足而托管
     * @return 是否因积分不足而托管
     */
    public boolean isInsufficientPointsTrusteeship() {
        return this.insufficientPointsTrusteeship;
    }

    /**
     * 设置托管 （带参数）
     *
     * @param flag
     */
    public void setTrusteeship(boolean flag) {
        super.setTrusteeship(flag);
        // 如果取消托管，同时清除积分不足标记
        if (!flag) {
            this.insufficientPointsTrusteeship = false;
        }
    }
}

package core.network.client2game.handler.zjh;

import business.zjh.c2s.iclass.CZJH_CreateRoom;
import business.player.Player;
import business.player.feature.PlayerUnionRoom;
import cenum.PrizeType;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;
import core.server.zjh.ZJHAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;

import java.io.IOException;

/**
 * 亲友圈房间
 *
 * <AUTHOR>
 */
public class CZJHUnionRoom extends PlayerHandler {

    @Override
    public void handle(Player player, WebSocketRequest request, String message)
            throws IOException {

        final CZJH_CreateRoom clientPack = new Gson().fromJson(message,
                CZJH_CreateRoom.class);

        // 公共房间配置
        BaseRoomConfigure<CZJH_CreateRoom> configure = new BaseRoomConfigure<CZJH_CreateRoom>(
                PrizeType.RoomCard,
                ZJHAPP.GameType(),
                clientPack.clone());
        player.getFeature(PlayerUnionRoom.class).createNoneUnionRoom(request, configure);
    }
}

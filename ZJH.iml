<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="EclipseModuleManager">
    <libelement value="jar://$MODULE_DIR$/../common/lib/Gson/gson-2.8.6.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/kernel.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/mockito-all-2.0.2-beta.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-api-mockito-1.6.3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-api-support-1.6.3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-core-1.6.3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-module-junit4-1.6.1.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-module-junit4-common-1.6.1.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-reflect-1.6.3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/javassist-3.25.0-GA.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/junit_mock/junit-4.12.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/apache-commons/commons-collections-3.2.2.jar!/" />
    <libelement value="jar://$MODULE_DIR$/../common/lib/commons-lang3-3.7/commons-lang3-3.7.jar!/" />
    <src_description expected_position="0">
      <src_folder value="file://$MODULE_DIR$/src" expected_position="0" />
    </src_description>
  </component>
  <component name="NewModuleRootManager">
    <output url="file://$MODULE_DIR$/bin" />
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
    </content>
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="inheritedJdk" />
    <orderEntry type="module" module-name="commdef" />
    <orderEntry type="module" module-name="common" />
    <orderEntry type="module" module-name="gameServer" />
    <orderEntry type="module-library">
      <library name="gson-2.8.6.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/Gson/gson-2.8.6.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="kernel.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/kernel.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="mockito-all-2.0.2-beta.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/mockito-all-2.0.2-beta.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="powermock-api-mockito-1.6.3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-api-mockito-1.6.3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="powermock-api-support-1.6.3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-api-support-1.6.3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="powermock-core-1.6.3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-core-1.6.3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="powermock-module-junit4-1.6.1.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-module-junit4-1.6.1.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="powermock-module-junit4-common-1.6.1.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-module-junit4-common-1.6.1.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="powermock-reflect-1.6.3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/powermock-reflect-1.6.3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="javassist-3.25.0-GA.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/javassist-3.25.0-GA.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="junit-4.12.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/junit_mock/junit-4.12.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-collections-3.2.2.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/apache-commons/commons-collections-3.2.2.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-lang3-3.7.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/../common/lib/commons-lang3-3.7/commons-lang3-3.7.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
  </component>
</module>
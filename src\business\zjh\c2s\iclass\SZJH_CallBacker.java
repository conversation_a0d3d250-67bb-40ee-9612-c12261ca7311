package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.*;

/**
 * 叫庄
 *
 * <AUTHOR>
 */
public class SZJH_CallBacker extends BaseSendMsg {

    public long roomID;
    public int pos;
    public int callBackerNum;//抢庄倍数

    public static SZJH_CallBacker make(long roomID, int pos, int callBackerNum) {
        SZJH_CallBacker ret = new SZJH_CallBacker();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.callBackerNum = callBackerNum;
        return ret;
    }
}

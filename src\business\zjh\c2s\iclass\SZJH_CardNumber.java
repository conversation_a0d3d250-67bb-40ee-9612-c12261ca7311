package business.zjh.c2s.iclass;

import java.util.Map;

import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 接收客户端数据
 * 记牌器
 *
 * <AUTHOR>
 */

@SuppressWarnings("serial")
public class SZJH_CardNumber extends BaseSendMsg {

    public long roomID;
    public Map<Integer, Integer> cardNumMap;

    public static SZJH_CardNumber make(long roomID, Map<Integer, Integer> cardNumMap) {
        SZJH_CardNumber ret = new SZJH_CardNumber();
        ret.cardNumMap = cardNumMap;
        ret.roomID = roomID;
        return ret;
    }
}

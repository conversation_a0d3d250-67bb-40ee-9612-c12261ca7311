package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;
import lombok.Data;

/**
 * 连接通知
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
@Data
public class SZJH_LostConnect extends BaseSendMsg {
    // 房间ID
    private long roomID;
    // 玩家PID
    private long pid;
    // T 离线,F 连接
    private boolean isLostCozjhect;
    private boolean isShowLeave;

    public static SZJH_LostConnect make(long roomID, long pid, boolean isLostCozjhect, boolean isShowLeave) {
        SZJH_LostConnect ret = new SZJH_LostConnect();
        ret.setRoomID(roomID);
        ret.setPid(pid);
        ret.setLostCozjhect(isLostCozjhect);
        ret.setShowLeave(isShowLeave);
        return ret;
    }
}

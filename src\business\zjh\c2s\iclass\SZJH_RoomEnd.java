package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.pk.PKRoom_Record;


public class SZJH_RoomEnd extends BaseSendMsg {

    public PKRoom_Record record;
    //public List<ZJHRoom_SetEnd> records;

    public static SZJH_RoomEnd make(PKRoom_Record record/*, List<ZJHRoom_SetEnd> records*/) {
        SZJH_RoomEnd ret = new SZJH_RoomEnd();
        ret.record = record;
        //ret.records = records;
        return ret;


    }
}

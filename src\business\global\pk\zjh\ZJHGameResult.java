package business.global.pk.zjh;

import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import business.zjh.c2s.cclass.ZJHRoomSet_Pos;
import business.zjh.c2s.cclass.ZJH_define;
import cenum.PrizeType;
import core.db.entity.clarkGame.GameSetBO;


public class ZJHGameResult {
    public ZJHRoom room; //房间

    public final int timesList[][] = {{1, 1, 1, 1, 1, 1, 1, 2, 2, 3, 4}, {1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 3}}; //倍数
    public final int specialtimesList[] = {1, 1, 5, 6, 8}; //倍数
    public final int specialIndexList[] = {-1, -1, 0, 1, 2}; //倍数
    public final int endPointList[] = {1, 2, 4};//底分
    public final int goleTimesList[] = {1, 1, 1, 1, 1, 1, 1, 2, 2, 3, 4};//金币翻倍规则
    public final int specialGoldTimesList[] = {1, 1, 5, 6, 8}; //倍数

    public ZJHGameResult(ZJHRoom room) {
        this.room = room;
    }

    //计算分数 和庄家比
    @SuppressWarnings("unchecked")
    public void calcByCallBacker() {
        ZJHRoomSet set = (ZJHRoomSet) this.room.getCurSet();
        int callBacketCardType = 0;
        ArrayList<Integer> callBackerCardList = new ArrayList<Integer>();
        for (int i = 0; i < this.room.getMaxPlayerNum(); i++) {
            if (!(boolean) set.playingList.get(i)) continue;
            ZJHRoomPos setpos = (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
//			CommLogD.info("calcByCallBacker setpos.privateCards:%s\n", setpos.privateCards.toString());
            int cardType = ZJHGameLogic.GetCardType(setpos.privateCards);
//			CommLogD.info(" calcByCallBacker  cardType:%d, setpos.privateCards:%s\n", cardType, setpos.privateCards.toString());
            if (i == set.getBackerPos()) {
                callBacketCardType = cardType;
                callBackerCardList = (ArrayList<Integer>) setpos.privateCards.clone();
            }
            set.crawTypeList.set(i, cardType);
        }
        ArrayList<Integer> tempPointList = (ArrayList<Integer>) set.pointList.clone();

        boolean isGold = PrizeType.Gold == this.room.getBaseRoomConfigure().getPrizeType() ? true : false;

        for (int i = 0; i < this.room.getMaxPlayerNum(); i++) {
            if (!(boolean) set.playingList.get(i)) continue;
            if (i == set.getBackerPos()) continue;

            ZJHRoomPos setpos = (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            boolean callbackerWin = false;
            int cardType = set.crawTypeList.get(i);
            if (ZJHGameLogic.CompareCard(callBackerCardList, setpos.privateCards,this.room.roomCfg.tongpaishuying)) {
                callbackerWin = true;
                cardType = callBacketCardType;
            }
            int point = 0;
            if (isGold) {
                point = calcGold(cardType, i);
            } else {
                point = calcPoint(cardType, i);
            }

            if (callbackerWin) {
                set.pointList.set(i, -point);
                set.pointList.set(set.getBackerPos(), set.pointList.get(set.getBackerPos()) + point);
            } else {
                set.pointList.set(i, point);
                set.pointList.set(set.getBackerPos(), set.pointList.get(set.getBackerPos()) - point);
            }
        }

        this.setWinOrLose(set.pointList, tempPointList);
    }

    //计算分数 和所有玩家比
    @SuppressWarnings("unchecked")
    public void calcByAll() {
        ZJHRoomSet set = (ZJHRoomSet) this.room.getCurSet();

        for (int i = 0; i < this.room.getMaxPlayerNum(); i++) {
            if (!(boolean) set.playingList.get(i)) continue;
            ZJHRoomPos setpos = (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            int cardType = ZJHGameLogic.GetCardType(setpos.privateCards);

            set.crawTypeList.set(i, cardType);
        }

        ArrayList<Integer> tempPointList = (ArrayList<Integer>) set.pointList.clone();

        boolean isGold = PrizeType.Gold == this.room.getBaseRoomConfigure().getPrizeType() ? true : false;

        for (int i = 0; i < this.room.getMaxPlayerNum(); i++) {
            if (!(boolean) set.playingList.get(i)) continue;
            ZJHRoomPos setpos_i = (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);

            for (int j = i + 1; j < this.room.getMaxPlayerNum(); j++) {
                if (!(boolean) set.playingList.get(j)) continue;
                if (i == j) continue;

                ZJHRoomPos setpos_j = (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(j);
                boolean isWin = false;
                int winCardType = set.crawTypeList.get(j);
                if (ZJHGameLogic.CompareCard(setpos_i.privateCards, setpos_j.privateCards,this.room.roomCfg.tongpaishuying)) {
                    isWin = true;
                    winCardType = set.crawTypeList.get(i);
                }
                int point = 0;

                if (isGold) {
                    point = calcGold(winCardType, isWin ? i : j);
                } else {
                    point = calcPoint(winCardType, isWin ? i : j);
                }

                if (!isWin) {
                    set.pointList.set(i, set.pointList.get(i) - point);
                    set.pointList.set(j, set.pointList.get(j) + point);

                } else {
                    set.pointList.set(i, set.pointList.get(i) + point);
                    set.pointList.set(j, set.pointList.get(j) - point);

                }
            }
        }

        this.setWinOrLose(set.pointList, tempPointList);
    }

    //设置输赢
    public void setWinOrLose(ArrayList<Integer> nowPointList, ArrayList<Integer> oldPointList) {
        ZJHRoomSet set = (ZJHRoomSet) this.room.getCurSet();
        for (int i = 0; i < this.room.getMaxPlayerNum(); i++) {
            if (!set.playingList.get(i)) continue;
            ZJHRoomPos roomPos = (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            if (nowPointList.get(i) > oldPointList.get(i)) {
                roomPos.addWin(1);
            } else if (nowPointList.get(i) == oldPointList.get(i)) {
                roomPos.addFlat(1);
            } else if (nowPointList.get(i) < oldPointList.get(i)) {
                roomPos.addLose(1);
            }
        }
    }


    //计算得分
    public int calcGold(int cardType, int pos) {
        int total = 0;
        ZJHRoomSet set = (ZJHRoomSet) this.room.getCurSet();
        int num = set.getCallbackerNum(set.getBackerPos());
        num = num > 0 ? num : 1;
        total = this.room.getBaseMark() * num * set.betList.get(pos) * this.getGoldTimes(cardType);
        return total;
    }

    //计算得分
    public int calcPoint(int cardType, int pos) {
        int total = 0;
        ZJHRoomSet set = (ZJHRoomSet) this.room.getCurSet();
//        if (this.room.getRoomCfg().getSign() == ZJH_define.ZJH_GameType.ZJH_TBZJH.value()) {
//            total = this.getTimes(cardType) * endPointList[this.room.getRoomCfg().difen];
//        } else {
//            total = this.getTimes(cardType) * set.betList.get(pos);
//        }
//
//        if (this.room.getRoomCfg().getSign() == ZJH_define.ZJH_GameType.ZJH_MPQZ.value()) {
//            int num = set.getCallbackerNum(set.getBackerPos());
//            num = num > 0 ? num : 1;
//            total = total * num;
//        }
        return total;
    }



    /**
     * 结算积分
     */
    public void calcPoint(){
//        HashMap<String, Object> map = new HashMap<String,  Object>();
//        map.put("roomID", room.getRoomID());
//        map.put("setID", this.room.getCurSetID());
//        GameSetBO gameSetBO =  BM.getBM(GameSetBO.class).findOne(map);
//        this.bo = gameSetBO == null ? new GameSetBO() : gameSetBO;
//
//        if(gameSetBO == null){
//            bo.setRoomID(room.getRoomID());
//            bo.setSetID(this.room.getCurSetID());
//        }
//
//        this.resultCalc();
//
//
//        for(int i = 0; i < this.room.getMaxPlayerNum(); i++){
//            ZJHRoomPos<?> roomPos = (ZJHRoomPos<?>) this.room.getPosMgr().getPos(i);
//
//            ZJHRoomSet_Pos posEnd = roomPos.calcPosEnd();
//            goldEnd(i, this.addScoreList.get(i));
//
//            this.setEnd.posResultList.add(posEnd);
//        }
//
//        room.getPosMgr().setAllLatelyOutCardTime();
//
//
//        this.setEnd.endTime = CommTime.nowSecond();
//
//        ZJHRoom_SetEnd lSetEnd = this.getNotify_setEnd();
//
//        String gsonSetEnd = new Gson().toJson(lSetEnd);
//        bo.setDataJsonRes(gsonSetEnd);
//        bo.setEndTime(setEnd.endTime);
//        if (gameSetBO != null) {
//            bo.saveAll_sync();
//        } else {
//            bo.insert_sync();
//        }
    }

    //获取倍数
    public int getTimes(int cardType) {
        int times = 1;
//        if (cardType > 10) {
//            int idx = cardType - ZJHGameLogic.OX_THREE_SAME;
//            if (this.getSpecialCardTypeSelected(this.specialIndexList[idx])) {
//                times = this.specialtimesList[idx];
//            } else {
////                int DoubleRules = this.room.getRoomCfg().fanbeiguize;
////                times = timesList[DoubleRules][10];
//            }
//        } else {
////            int DoubleRules = this.room.getRoomCfg().fanbeiguize;
////            times = timesList[DoubleRules][cardType];
//        }
        return times;
    }

    //获取金币倍数
    public int getGoldTimes(int cardType) {
        int times = 1;
//        if (cardType > 10) {
//            int idx = cardType - ZJHGameLogic.OX_THREE_SAME;
//            times = this.specialGoldTimesList[idx];
//        } else {
//            times = this.goleTimesList[cardType];
//        }
        return times;
    }

    //特殊牌型
    public boolean getSpecialCardTypeSelected(int idx) {
//        for (Integer index : this.room.getRoomCfg().teshupaixing) {
//            if (idx == index) return true;
//        }
        return false;
    }

    /**
     * 结算进墓园
     */
    public void calcJinMuYuan(){
        //墓园，先结算比庄家小的人，再结算大于庄家的
        ZJHRoomSet set = (ZJHRoomSet) this.room.getCurSet();
        int bankerPosIndex = set.getBackerPos();
        if(bankerPosIndex<0)return;
        //结算信息
        ZJHResultSetInfo bankerSetInfo = new ZJHResultSetInfo();
        ZJHRoomPos bankerPos = (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(bankerPosIndex);
        //设置结算信息
        bankerSetInfo.setCardType(ZJHGameLogic.GetCardType(bankerPos.privateCards));
        bankerSetInfo.setPrivateCards(bankerPos.privateCards);
        bankerSetInfo.setBankerPoint(bankerPos.getPoint());
        //设置牌型
        IntStream.range(0,this.room.getMaxPlayerNum()).filter(i->set.playingList.get(i)).forEach(i->{
            ZJHRoomPos setPos = (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            set.crawTypeList.set(i, ZJHGameLogic.GetCardType(setPos.privateCards));
        });
        //庄家分数
        ArrayList<Integer> oldPointList = (ArrayList<Integer>) set.pointList.clone();
        Map<Boolean, List<ZJHRoomPos>> wizjherLoserList = IntStream.range(0, this.room.getMaxPlayerNum()).filter(i -> set.playingList.get(i) && i != set.getBackerPos()).mapToObj(i -> (ZJHRoomPos) this.room.getRoomPosMgr().getPosByPosID(i)).collect(Collectors.groupingBy(n -> ZJHGameLogic.CompareCard(bankerSetInfo.getPrivateCards(), n.getPrivateCards(),this.room.roomCfg.tongpaishuying)));
        //计算输庄家的人的钱
        if(wizjherLoserList.containsKey(true)){
            wizjherLoserList.get(true).forEach(n->{
                int point = calcPoint(bankerSetInfo.getCardType(), n.getPosID());
                set.pointList.set(n.getPosID(), -point);
                set.pointList.set(set.getBackerPos(), set.pointList.get(bankerPosIndex) + point);
                bankerSetInfo.setBankerPoint(bankerSetInfo.getBankerPoint()+point);
            });
        }
        //计算赢庄家的人的钱
        if(wizjherLoserList.containsKey(false)){
            wizjherLoserList.get(false).sort((o2,o1)->{
                if(ZJHGameLogic.CompareCard(o2.getPrivateCards(), o1.getPrivateCards(),this.room.roomCfg.tongpaishuying)){
                    return -1;
                }
                return 0;
            });
            wizjherLoserList.get(false).forEach(n->{
                int point = calcPoint(set.crawTypeList.get(n.getPosID()), n.getPosID());
                int lastPoint = bankerSetInfo.getBankerPoint() - point;
                if(lastPoint<=0){
                    point = bankerSetInfo.getBankerPoint();
                }
                set.pointList.set(n.getPosID(), point);
                set.pointList.set(set.getBackerPos(), set.pointList.get(bankerPosIndex) - point);
                bankerSetInfo.setBankerPoint(bankerSetInfo.getBankerPoint()-point);
            });
        }
        this.setWinOrLose(set.pointList, oldPointList);
    }

}

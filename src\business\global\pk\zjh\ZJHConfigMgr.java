package business.global.pk.zjh;

import business.global.config.GameListConfigMgr;
import com.ddm.server.common.utils.Txt2Utils;
import core.server.zjh.ZJHAPP;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * 配置文件
 * <AUTHOR>
 * */
public class ZJHConfigMgr {
	public static final String fileName = "ZJHConfig.txt";
	public static final String filePath = "conf/";
	private Map<String, String> configMap = new HashMap<String, String>();
	private ArrayList<Integer> difenList; //底分
	private ArrayList<Integer> bottomPointList; //底注、
	private ArrayList<Integer> topPointList; //顶注
	private ArrayList<Integer> comporeCountList;  		//可比轮数
	private ArrayList<Integer> xiQianBeiShuList;  		//喜钱轮数
	private ArrayList<Integer> lunshushangxian;  				//轮数
	private int  robotOpenCard  = 50;
	private int  robotQiPai  = 50;
	private int  robotJiaZhu  = 50;	//机器人加注
	private int  addScoreAll  = 5; //默认
	private ArrayList<Integer> robotJiaZhuList;  		//#加注等级
	protected int God_Card ;
	protected ArrayList<Integer> Private_Card1;
	protected ArrayList<Integer> Private_Card2;
	protected ArrayList<Integer> Private_Card3;
	protected ArrayList<Integer> Private_Card4;
	protected ArrayList<Integer> Private_Card5;
	protected ArrayList<Integer> Private_Card6;
	protected ArrayList<Integer> Private_Card7;
	protected ArrayList<Integer> Private_Card8;
	protected ArrayList<Integer> Private_Card9;
	protected ArrayList<Integer> Private_Card10;
	private List<Integer> pidList;
	public ZJHConfigMgr(){
		this.configMap = Txt2Utils.txt2Map(filePath, fileName, "GBK");
		this.God_Card = Integer.valueOf(this.configMap.get("God_Card"));
		this.difenList = Txt2Utils.String2ListInteger(this.configMap.get("difenList"));
		this.bottomPointList = Txt2Utils.String2ListInteger(this.configMap.get("bottomPoint"));
		this.topPointList = Txt2Utils.String2ListInteger(this.configMap.get("topPoint"));
		this.comporeCountList = Txt2Utils.String2ListInteger(this.configMap.get("comporeCount"));
		this.xiQianBeiShuList = Txt2Utils.String2ListInteger(this.configMap.get("xiQianBeiShu"));
		this.lunshushangxian = Txt2Utils.String2ListInteger(this.configMap.get("lunshushangxian"));
		this.robotOpenCard = Integer.valueOf(this.configMap.get("robotOpenCard"));
		this.robotQiPai = Integer.valueOf(this.configMap.get("robotQiPai"));
		this.robotJiaZhu = Integer.valueOf(this.configMap.get("robotJiaZhu"));
		this.addScoreAll = Integer.valueOf(this.configMap.get("addScoreAll"));
		this.robotJiaZhuList = Txt2Utils.String2ListInteger(this.configMap.get("robotJiaZhuList"));
		this.Private_Card1 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card1"));
		this.Private_Card2 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card2"));
		this.Private_Card3 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card3"));
		this.Private_Card4 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card4"));
		this.Private_Card5 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card5"));
		this.Private_Card6 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card6"));
		this.Private_Card7 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card7"));
		this.Private_Card8 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card8"));
		this.Private_Card9 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card9"));
		this.Private_Card10 = Txt2Utils.String2ListInteger(this.configMap.get("Private_Card10"));

		this.pidList = this.configMap.containsKey("pidList") ? Txt2Utils.String2ListInteger(this.configMap.get("pidList")) : new ArrayList<>();
		String pidStr = GameListConfigMgr.getInstance().getPidList(ZJHAPP.gameTypeId);
		if (!StringUtils.isEmpty(pidStr)) {
			this.pidList.addAll(Txt2Utils.String2ListInteger("["+pidStr+"]"));
		}
	}

	/**
	 * @return private_Card1
	 */
	public ArrayList<Integer> getPrivate_Card1() {
		return Private_Card1;
	}
	/**
	 * @return private_Card2
	 */
	public ArrayList<Integer> getPrivate_Card2() {
		return Private_Card2;
	}
	/**
	 * @return private_Card3
	 */
	public ArrayList<Integer> getPrivate_Card3() {
		return Private_Card3;
	}
	/**
	 * @return private_Card4
	 */
	public ArrayList<Integer> getPrivate_Card4() {
		return Private_Card4;
	}
	/**
	 * @return private_Card5
	 */
	public ArrayList<Integer> getPrivate_Card5() {
		return Private_Card5;
	}
	/**
	 * @return private_Card6
	 */
	public ArrayList<Integer> getPrivate_Card6() {
		return Private_Card6;
	}
	/**
	 * @return private_Card7
	 */
	public ArrayList<Integer> getPrivate_Card7() {
		return Private_Card7;
	}
	/**
	 * @return private_Card8
	 */
	public ArrayList<Integer> getPrivate_Card8() {
		return Private_Card8;
	}
	public ArrayList<Integer> getPrivate_Card9() {
		return Private_Card9;
	}
	public ArrayList<Integer> getPrivate_Card10() {
		return Private_Card10;
	}

	/**
	 * @return god_Card
	 */
	public boolean isGodCard() {
		return God_Card == 1;
	}
	/**
	 * @return endPointList
	 */
	public ArrayList<Integer> getEndPointList() {
		return this.difenList;
	}


	/**
	 * @return bottomPointList
	 */
	public ArrayList<Integer> getBottomPointList() {
		return bottomPointList;
	}


	/**
	 * @return comporeCountList
	 */
	public ArrayList<Integer> getComporeCountList() {
		return comporeCountList;
	}


	/**
	 * @return robotOpenCard
	 */
	public int getRobotOpenCard() {
		return robotOpenCard;
	}


	/**
	 * @return robotJiaZhu
	 */
	public int getRobotJiaZhu() {
		return robotJiaZhu;
	}


	/**
	 * @return robotJiaZhuList
	 */
	public ArrayList<Integer> getRobotJiaZhuList() {
		return robotJiaZhuList;
	}


	/**
	 * @return xiQianBeiShuList
	 */
	public ArrayList<Integer> getXiQianBeiShuList() {
		return xiQianBeiShuList;
	}


	/**
	 * @return addScoreAll
	 */
	public int getAddScoreAll() {
		return addScoreAll;
	}


	/**
	 * @return robotQiPai
	 */
	public int getRobotQiPai() {
		return robotQiPai;
	}


	/**
	 * @return topPointList
	 */
	public ArrayList<Integer> getTopPointList() {
		return topPointList;
	}


	/**
	 * @return turnsList
	 */
	public ArrayList<Integer> getLunShuShangXian() {
		return lunshushangxian;
	}

	public List<Integer> getPidList() {
		return pidList;
	}
}


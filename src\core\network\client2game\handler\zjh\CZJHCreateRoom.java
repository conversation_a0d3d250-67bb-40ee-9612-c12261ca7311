package core.network.client2game.handler.zjh;

import java.io.IOException;

import business.zjh.c2s.iclass.CZJH_CreateRoom;
import core.network.http.proto.SData_Result;
import core.server.zjh.ZJHAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;
import business.player.Player;
import business.player.feature.PlayerRoom;
import cenum.PrizeType;

import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.exception.WSException;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;

import core.network.client2game.handler.PlayerHandler;

public class CZJHCreateRoom extends PlayerHandler {

    @Override
    public void handle(Player player, WebSocketRequest request, String message) throws WSException, IOException {
        // TODO 自动生成的方法存根
        final CZJH_CreateRoom clientPack = new Gson().from<PERSON><PERSON>(message,
                CZJH_CreateRoom.class);
        // 公共房间配置
        BaseRoomConfigure<CZJH_CreateRoom> configure = new BaseRoomConfigure<CZJH_CreateRoom>(
                PrizeType.RoomCard,
                ZJHAPP.GameType(),
                clientPack.clone());
        SData_Result resule = player.getFeature(PlayerRoom.class).createRoomAndConsumeCard(configure);
        if (ErrorCode.Success.equals(resule.getCode())) {
            request.response(resule.getData());
        } else {
            request.error(resule.getCode(), resule.getMsg());
        }
    }

}

package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

@SuppressWarnings("serial")
public class CZJH_SendGift extends BaseSendMsg {
    public long roomID;
    public int pos;
    public long productId;

    public static CZJH_SendGift make(long roomID, int pos, long productId) {
        CZJH_SendGift ret = new CZJH_SendGift();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.productId = productId;
        return ret;
    }
}

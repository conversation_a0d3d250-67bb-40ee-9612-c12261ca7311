package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.*;


@SuppressWarnings("serial")
public class SZJH_StartRound<T> extends BaseSendMsg {

    public long roomID;
    public T room_SetWait;


    public static <T> SZJH_StartRound<T> make(long roomID, T room_SetWait) {
        SZJH_StartRound<T> ret = new SZJH_StartRound<T>();
        ret.roomID = roomID;
        ret.room_SetWait = room_SetWait;

        return ret;


    }
}

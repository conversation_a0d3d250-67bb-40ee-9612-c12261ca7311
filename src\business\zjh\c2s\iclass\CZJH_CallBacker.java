package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.*;

/**
 * 叫庄
 *
 * <AUTHOR>
 */
public class CZJH_CallBacker extends BaseSendMsg {

    public long roomID;
    public int pos;
    public int callBackerNum;//抢庄倍数

    public static CZJH_CallBacker make(long roomID, int pos, int callBackerNum) {
        CZJH_CallBacker ret = new CZJH_CallBacker();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.callBackerNum = callBackerNum;
        return ret;


    }
}

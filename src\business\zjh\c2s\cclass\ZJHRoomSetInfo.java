package business.zjh.c2s.cclass;

import business.zjh.c2s.iclass.SZJH_SetEnd;
import cenum.room.SetState;
import jsproto.c2s.cclass.pk.Victory;
import jsproto.c2s.cclass.room.RoomSetInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 跑得快 当前局游戏信息
 *
 * <AUTHOR>
 */
public class ZJHRoomSetInfo extends RoomSetInfo {

//    public int  setID = 0; // 游戏局ID
    public long startTime = 0;
    public int state = SetState.Init.value(); // 游戏状态
    public int  backerPos = -1;// 当前庄家
    public long bottomPoint = 0;//底注
    public int opPos = -1;				//操作位置
    public int currTurns = 0;// 当前轮数
    public int upwardTimePos=-1;//上分位置
    public int upwardOpTime=0;//玩家点击上分按钮的时间
    public int upwardTime=0;//上分时间，60，120，180，240秒

    public List<ZJHRoomSet_Pos> posInfo = new ArrayList<>(); // 一局玩家列表


}

package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 接收客户端数据
 * 加倍
 *
 * <AUTHOR>
 */

@SuppressWarnings("serial")
public class SZJH_AddDouble extends BaseSendMsg {

    public long roomID;
    public int pos;  //位置
    public int addDouble;


    public static SZJH_AddDouble make(long roomID, int pos, int addDouble) {
        SZJH_AddDouble ret = new SZJH_AddDouble();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.addDouble = addDouble;
        return ret;
    }
}

package core.network.client2game.handler.zjh;

import business.global.pk.zjh.ZJHRoom;
import business.global.pk.zjh.ZJHRoomSet;
import business.global.room.RoomMgr;
import business.player.Player;
import business.zjh.c2s.iclass.CZJH_WinControl;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.exception.WSException;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;

import java.io.IOException;

/**
 * 设置测试
 */
public class CZJHWinControl extends PlayerHandler {

    @Override
    public void handle(Player player, WebSocketRequest request, String message) throws WSException, IOException {
        final CZJH_WinControl req = new Gson().fromJson(message, CZJH_WinControl.class);
        long roomID = req.roomID;

        ZJHRoom room = (ZJHRoom) RoomMgr.getInstance().getRoom(roomID);
        if (null == room) {
            request.error(ErrorCode.NotAllow, "CZJHWinControl not find room:" + roomID);
            return;
        }
        ZJHRoomSet set = (ZJHRoomSet) room.getCurSet();
        if (null == set) {
            request.error(ErrorCode.NotAllow, "CZJHWinControl not set room:" + roomID);
            return;
        }
        //如果没有特殊权限，直接返回
        if (!set.isGodUser(player.getPid())) {
            request.error(ErrorCode.NotAllow, "CZJHWinControl not godUser:" + req.roomID);
            return;
        }
        set.onWinControl(req);
        request.response();
    }

}

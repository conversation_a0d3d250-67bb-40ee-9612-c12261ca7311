package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;


public class SZJH_ChangePlayerNumAgree extends BaseSendMsg {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    public long roomID;
    public int pos;
    public boolean agreeChange;

    public static SZJH_ChangePlayerNumAgree make(long roomID, int pos, boolean agreeChange) {
        SZJH_ChangePlayerNumAgree ret = new SZJH_ChangePlayerNumAgree();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.agreeChange = agreeChange;
        return ret;


    }
}

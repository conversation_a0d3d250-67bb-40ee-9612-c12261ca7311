﻿#0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, //方块A~K
#0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, //梅花A~K
#0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, //红桃A~K
#0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, //黑桃A~K
#神牌模式(0:关,1:开)
God_Card  = 0;
#玩家一
Private_Card1 = [];
#玩家二
Private_Card2 = [0x03,0x02,0x01,0x11,0x21];
#玩家三
Private_Card3 = [0x1A,0x2A,0x3A,0x0A,0x06];
#玩家四
Private_Card4 = [0x0B,0x1B,0x2B,0x0C,0x1C];
#玩家五
Private_Card5 = [];
#玩家六
Private_Card6 = [];
#玩家七
Private_Card7 = [];
#玩家八
Private_Card8 = [];
Private_Card9 = [];

Private_Card10 = [];

God_Card  = 0;

#底注
bottomPoint =[1,2,3,5,10,20];
#顶注
topPoint =[5,10];
#可比轮数
comporeCount = [2,3,4];
#炸金花底分配置
difenList = [1,2,3,4,5,10];
#机器人看牌的概率
robotOpenCard = 80;
#机器人棋牌的概率
robotQiPai = 50;
#机器人加注的概率
robotJiaZhu = 20;
#加注等级
robotJiaZhuList = [1,2,3,4,5];
#喜钱陪数
xiQianBeiShu=[3,5];
#全压顶注的5倍
addScoreAll = 5;
#轮数
lunshushangxian=[10,20,30];


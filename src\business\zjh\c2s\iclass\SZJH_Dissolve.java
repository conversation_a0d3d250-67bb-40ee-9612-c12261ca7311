package business.zjh.c2s.iclass;

import jsproto.c2s.iclass.room.SBase_Dissolve;

/**
 * 房间解散通知
 *
 * <AUTHOR>
 */
public class SZJH_Dissolve extends SBase_Dissolve {


    /**
     *
     */
    private static final long serialVersionUID = 1L;

    public static SZJH_Dissolve make(SBase_Dissolve dissolve) {
        SZJH_Dissolve ret = new SZJH_Dissolve();
        ret.setOwnnerForce(dissolve.isOwnnerForce());
        ret.setRoomID(dissolve.getRoomID());
        ret.setDissolveNoticeType(dissolve.getDissolveNoticeType());
        ret.setMsg(dissolve.getMsg());
        return ret;
    }
}

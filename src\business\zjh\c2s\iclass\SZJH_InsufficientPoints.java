package business.zjh.c2s.iclass;

/**
 * 积分不足提示消息
 */
@SuppressWarnings("serial")
public class SZJH_InsufficientPoints extends jsproto.c2s.cclass.BaseSendMsg {
    
    public long roomID;
    public int posId;
    public long pid;
    public int upwardOpTime; // 操作时间
    public int upwardTime; // 倒计时时间
    public int serverTime; // 服务器时间
    
    public static SZJH_InsufficientPoints make(long roomID, int posId, long pid, int countdownTime) {
        SZJH_InsufficientPoints ret = new SZJH_InsufficientPoints();
        ret.roomID = roomID;
        ret.posId = posId;
        ret.pid = pid;
        ret.upwardOpTime = (int)(System.currentTimeMillis() / 1000);
        ret.upwardTime = countdownTime; // 设置倒计时时间为传入的值
        ret.serverTime = (int)(System.currentTimeMillis() / 1000);
        return ret;
    }
} 
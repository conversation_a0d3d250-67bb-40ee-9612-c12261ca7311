package business.zjh.c2s.iclass;

import java.util.ArrayList;
import java.util.List;

import jsproto.c2s.cclass.BaseSendMsg;
import lombok.Data;

/**
 * 接收客户端数据
 * 加倍
 *
 * <AUTHOR>
 */
@Data
public class CZJH_OpCard extends BaseSendMsg {

    public long roomID;
    public int pos;  //位置
    public int opType;  //PDK_CARD_TYPE 操作类型及牌的类型
    public List<Integer> cardList;
    public int addBet;  //押注分数
    public int comporePos;//比较位置


    public static CZJH_OpCard make(long roomID, int pos,int opType,List<Integer> cardList, int addBet, int comporePos) {
        CZJH_OpCard ret = new CZJH_OpCard();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.opType = opType;
        ret.addBet = addBet;
        ret.comporePos = comporePos;
        return ret;
    }
}

package business.zjh.c2s.iclass;

import cenum.RoomTypeEnum;
import jsproto.c2s.cclass.room.BaseCreateRoom;
import jsproto.c2s.iclass.room.SBase_Config;


@SuppressWarnings("serial")
public class SZJH_Config extends SBase_Config {
    public static SZJH_Config make(BaseCreateRoom cfg, RoomTypeEnum roomTypeEnum) {
        SZJH_Config ret = new SZJH_Config();
        ret.setCfg(cfg);
        ret.setRoomType(roomTypeEnum);
        return ret;
    }
}

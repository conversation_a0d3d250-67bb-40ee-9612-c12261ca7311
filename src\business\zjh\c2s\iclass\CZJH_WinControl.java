package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 测试 panvc
 */
public class CZJH_WinControl extends BaseSendMsg {
    
    public long roomID;
    public int pos; //被控制人的位置
    public int flag;//0 输  1 赢

    public static CZJH_WinControl make(long roomID, int pos, int flag) {
        CZJH_WinControl ret = new CZJH_WinControl();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.flag = flag;
        return ret;
    

    }
}

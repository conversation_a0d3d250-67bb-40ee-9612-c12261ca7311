package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;


public class SZJH_UpwardPoint<T> extends BaseSendMsg {

    public long roomID;
    public int posId;
    public long pid;
    //玩家点击上分按钮的时间
    public int upwardOpTime;
    //上分时间，60，120，180，240秒
    public int upwardTime;
    //服务器时间
    public int serverTime;

    public static SZJH_UpwardPoint make(long roomID, int posId, long pid, int upwardOpTime, int upwardTime, int serverTime) {
        SZJH_UpwardPoint ret = new SZJH_UpwardPoint();
        ret.roomID = roomID;
        ret.posId = posId;
        ret.pid = pid;
        ret.upwardOpTime = upwardOpTime;
        ret.upwardTime = upwardTime;
        ret.serverTime = serverTime;
        return ret;

    }
}

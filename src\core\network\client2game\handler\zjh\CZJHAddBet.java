package core.network.client2game.handler.zjh;

import java.io.IOException;

import business.global.pk.zjh.ZJHRoom;
import business.global.pk.zjh.ZJHRoomSet;
import business.global.room.RoomMgr;
import business.zjh.c2s.iclass.CZJH_AddBet;
import business.player.Player;

import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.exception.WSException;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;

import core.network.client2game.handler.PlayerHandler;

/**
 * 确认牌的顺序
 *
 * <AUTHOR>
 */
public class CZJHAddBet extends PlayerHandler {

    @Override
    public void handle(Player player, WebSocketRequest request, String message)
            throws WSException, IOException {
        final CZJH_AddBet req = new Gson().fromJson(message, CZJH_AddBet.class);
        long roomID = req.roomID;

        ZJHRoom room = (ZJHRoom) RoomMgr.getInstance().getRoom(roomID);
        if (null == room) {
            request.error(ErrorCode.NotAllow, "CZJHAddBet not find room:" + roomID);
            return;
        }
        ZJHRoomSet set = (ZJHRoomSet) room.getCurSet();
        if (null == set) {
            request.error(ErrorCode.NotAllow, "CZJHAddBet not set room:" + roomID);
            return;
        }
        set.onAddBet(request, req);
    }

}

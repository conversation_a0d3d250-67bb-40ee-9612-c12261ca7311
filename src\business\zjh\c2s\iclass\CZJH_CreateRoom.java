package business.zjh.c2s.iclass;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

import com.ddm.server.common.CommLogD;
import jsproto.c2s.cclass.room.BaseCreateRoom;
import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 牛牛
 * 接收客户端数据
 * 创建房间
 *
 * <AUTHOR>
 */

public class CZJH_CreateRoom extends BaseCreateRoom implements Cloneable, Serializable {

    public int 		difen = 0; 				//底分
    public int 		dingzhu  = 0;				//顶注
    public int 		dizhu  = 0;				//底注
    public int 		kebilunshu = 0;  		//可比轮数
    public List<Integer> xiqian = new ArrayList<>();
    public int 		lunshushangxian = 2;//轮数上限
    public List<Integer> gaojixuanxiang = new ArrayList<>();//游戏开始后禁止加入,禁止搓牌


    public int 		tongpaishuying = 0;  		//同牌输赢： 先比为输 按花色比
    public int 		caozuoshijian = 0;  		//操作时间： 15s弃牌 30s弃牌 60s弃牌 caozuoshijian =0或者1或者2
    public int 		menpailunshu = 0;  		//闷牌轮数： 不闷 前注1倍 前注2倍 前注3倍 前注5倍 menpailunshu=0 或者1或者2
//2、、、、
//    let arr = [0,1,2,3,5]
//    当闷牌轮数大于0，在游戏开始的时候"SZJH_SetStart"这个消息里面serverPack["setInfo"]的
//            bottomPoint = dizhu分数乘以arr [menpailunshu]
//    public boolean 	gaojixuanxiang = false;//游戏开始后禁止进入
//    public boolean 	xiqian = false;		//喜钱

    public int bimen; //必闷 前端显示用 panvc

    public int sbbp;//双倍比牌 前端显示用 panvc

    /**
     * 对象之间的浅克隆【只负责copy对象本身，不负责深度copy其内嵌的成员对象】
     *
     * @return
     */
    @Override
    public CZJH_CreateRoom clone() {
        return (CZJH_CreateRoom) super.clone();
    }

    /**
     * 实现对象间的深度克隆【从外形到内在细胞，完完全全深度copy】
     *
     * @return
     */
    public CZJH_CreateRoom deepClone() {
        // Anything 都是可以用字节流进行表示，记住是任何！
        CZJH_CreateRoom cookBook = null;
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            // 将当前的对象写入baos【输出流 -- 字节数组】里
            oos.writeObject(this);

            // 从输出字节数组缓存区中拿到字节流
            byte[] bytes = baos.toByteArray();

            // 创建一个输入字节数组缓冲区
            ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
            // 创建一个对象输入流
            ObjectInputStream ois = new ObjectInputStream(bais);
            // 下面将反序列化字节流 == 重新开辟一块空间存放反序列化后的对象
            cookBook = (CZJH_CreateRoom) ois.readObject();

        } catch (Exception e) {
            CommLogD.error(e.getClass() + ":" + e.getMessage());
        }
        return cookBook;
    }


    @Override
    public int hashCode() {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    @Override
    public boolean equals(Object obj) {
        return EqualsBuilder.reflectionEquals(this, obj);
    }
}

package core.network.client2game.handler.zjh;

import business.global.pk.zjh.ZJHRoom;
import business.global.pk.zjh.ZJHRoomSet;
import business.global.room.RoomMgr;
import business.player.Player;
import business.zjh.c2s.iclass.CZJH_OpCard;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.exception.WSException;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;

import java.io.IOException;

/**
 * 玩家操作
 * <AUTHOR>
 *
 */
public class CZJHOp extends PlayerHandler {

	@Override
	public void handle(Player player, WebSocketRequest request, String message)
			throws WSException, IOException {
    	final CZJH_OpCard req = new Gson().fromJson(message, CZJH_OpCard.class);
    	long roomID = req.roomID;

    	ZJHRoom room = (ZJHRoom ) RoomMgr.getInstance().getRoom(roomID);
    	if (null == room){
    		request.error(ErrorCode.NotAllow, "CZJHOp not find room:"+roomID);
    		return;
    	}
    	ZJHRoomSet set =  (ZJHRoomSet) room.getCurSet();
    	if(null == set){
    		request.error(ErrorCode.NotAllow, "CZJHOp not set room:"+roomID);
    		return;
    	}
    	set.onOp(request,  req);	
	}

}

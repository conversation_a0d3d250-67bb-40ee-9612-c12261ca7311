package business.global.pk.zjh;

import business.global.room.base.AbsBaseRoom;
import business.global.room.base.AbsRoomPos;
import business.global.room.base.AbsRoomPosMgr;
import business.player.Robot.RobotMgr;
import business.zjh.c2s.iclass.CZJH_CreateRoom;
import business.zjh.c2s.iclass.SZJH_Trusteeship;
import cenum.room.SetState;
import com.ddm.server.common.utils.CommMath;
import com.ddm.server.common.utils.CommTime;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.pos.RoomPlayerPos;
import jsproto.c2s.iclass.room.SRoom_SportsPointChange;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ZJHRoomPosMgr extends AbsRoomPosMgr {

    @SuppressWarnings({ "rawtypes", "unchecked" })
    public ZJHRoomPosMgr(AbsBaseRoom room) {
        super(room);
    }

    public int getAllPlayerNum() {
        int count = 0;
        for (AbsRoomPos pos : this.getPosList()) {
            if (pos.getPid() <= 0) {
                continue;
            }
            count++;
        }
        return count;
    }

    /**
     * 是否所有玩家准备
     *
     * @return
     */
    @Override
    public boolean isAllReady() {

        if (this.room.getBaseRoomConfigure().getBaseCreateRoom().getPlayerNum() == this.room.getBaseRoomConfigure()
                .getBaseCreateRoom().getPlayerMinNum()) {
            if (null == this.getPosList() || this.getPosList().size() <= 1) {
                // 玩家信息列表没数据
                return false;
            }
            AbsRoomPos result = this.getPosList().stream().filter((x) -> !x.isReady()).findAny().orElse(null);
            if (null != result) {
                return false;
            }
            return true;
        } else {
            List<AbsRoomPos> result = this.getPosList().stream().filter((x) -> x.getPid() != 0)
                    .collect(Collectors.toList());
            if (null == this.getPosList() || this.getPosList().size() <= 1) {
                // 玩家信息列表没数据 人数少于两个无法开始
                return false;
            }
            if (null == result || result.size() < 2) {
                // 房间玩家 人数少于两个无法开始
                return false;
            }
            for (AbsRoomPos con : result) {
                if (!con.isReady()) {
                    return false;
                }
            }
            return true;
        }
    }

    @Override
    protected void initPosList() {
        // 初始化房间位置
        for (int posID = 0; posID < this.getPlayerNum(); posID++) {
            this.getAllPosList().add(new ZJHRoomPos(posID, room));
        }
        for (int posID = 0; posID < getVisitListSize(); posID++) {
            this.getAllPosList().add(new ZJHRoomPos(posID, true, room));
        }
    }

    // 获取玩过这个游戏的人数
    public int getPlayTheGameNum() {
        int count = 0;
        for (AbsRoomPos roomPosDelegateAbstract : getPosList()) {
            ZJHRoomPos roomPos = (ZJHRoomPos) roomPosDelegateAbstract;
            if (roomPos.isPlayTheGame()) {
                count++;
            }
        }
        return count;
    }

    /**
     * 通知所有人房间竞技点更新
     */
    public boolean notify2RoomSportsPointChange(long pid, long memberId, double value) {
        if (CollectionUtils.isEmpty(this.getPosList())) {
            // 玩家信息列表没数据
            return false;
        }
        AbsRoomPos roomPos = this.getAllPosByPid(pid);
        if (Objects.isNull(roomPos)) {
            return false;
        }
        if (memberId > 0L && memberId == roomPos.memberId()) {
            roomPos.setRoomSportsPoint(CommMath.addDouble(roomPos.getRoomSportsPointValue(), value));
            if (value > 0) {
                roomPos.setOtherSportsPointConsume(CommMath.subDouble(roomPos.getOtherSportsPointConsume(), value));
            } else if (value < 0) {
                roomPos.setOtherSportsPointConsume(CommMath.addDouble(roomPos.getOtherSportsPointConsume(), value));
            }
            BaseSendMsg msg = SRoom_SportsPointChange.make(this.room.getRoomID(), roomPos.getPosID(), pid, value);

            ZJHRoomSet roomSet = ((ZJHRoomSet) this.room.getCurSet());
            if (roomSet.upwardTimePos == roomPos.getPosID()) {
                roomSet.upwardTimePos = -1;
            }

            if (this.room.getCurSet() != null) {
                // 添加到回放记录
                ((ZJHRoomSet) this.room.getCurSet()).getRoomPlayBack().addPlaybackList(msg, null);
            }

            this.getAllPosList().forEach(key -> {
                if (Objects.nonNull(key) && !key.isRobot() && key.getPid() > 0L) {
                    key.getPlayer().pushProto(msg);
                }
            });
            return true;
        }
        return false;
    }

    /**
     * 当所有人取消所有托管状态
     */
    public boolean notAllTrusteeship() {
        for (int i = 0; i < getPlayerNum(); i++) {
            AbsRoomPos pos = getPosList().get(i);
            if (null != pos) {
                if (pos.isTrusteeship()) {
                    return false;
                }
                if (pos.isRobot()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 获取所有玩家的牌
     *
     */
    @SuppressWarnings("unchecked")
    public ArrayList<ArrayList<Integer>> getAllPlayBackNotify() {
        return (ArrayList<ArrayList<Integer>>) this.getPosList().stream().filter(x -> x.getPid() > 0)
                .map(k -> (ArrayList<Integer>) ((ZJHRoomPos) k).getPrivateCards().clone()).collect(Collectors.toList());
    }

    /**
     * 玩家位置基本信息
     *
     * @return
     */
    @Override
    public List<RoomPlayerPos> getRoomPlayerPosList() {
        return this.getPosList().stream()
                .filter(k -> Objects.nonNull(k) && k.getPid() > 0L && k.isPlayTheGame())
                .map(AbsRoomPos::roomPlayerPos).sorted(Comparator.comparing(RoomPlayerPos::getPos))
                .collect(Collectors.toList());
    }

    /**
     * 是否所有玩家继续下一局
     *
     * @return
     */
    @Override
    public boolean isAllContinue() {
        if (null == this.getPosList() || this.getFullPosCount() <= 1) {
            // 玩家信息列表没数据
            return false;
        }
        // 超时继续，萍乡
        this.getPosList().stream().forEach(k -> {
            if ((k.getPid() > 0 && !k.isGameReady() && k.getTimeSec() > 0 && CommTime.nowSecond() - k.getTimeSec() >= 5)
                    || k.isLostConnect()) {
                getRoom().continueGame(k.getPid());
            }
        });
        // 玩家在游戏中并且没有准备。
        return this.getPosList().stream().allMatch(k -> k.getPid() <= 0L || (k.getPid() > 0L && k.isGameReady()));
    }

    @Override
    public void checkOverTime(int ServerTime) {
        // 检查是否有玩家正在上分中
        ZJHRoomSet roomSet = (ZJHRoomSet) this.getRoom().getCurSet();
        boolean hasUpwardPlayer = false;
        
        if (roomSet != null && roomSet.upwardTimePos != -1) {
            hasUpwardPlayer = true;
        }
        
        // 如果有玩家正在上分，暂时跳过父类的超时检查，避免误触发托管
        if (!hasUpwardPlayer) {
            super.checkOverTime(ServerTime);
        }
        
        // 始终检查积分不足托管状态的恢复
        checkInsufficientPointsTrusteeship();
    }
    
    /**
     * 检查积分不足的托管玩家，如果积分已充足则恢复状态
     */
    private void checkInsufficientPointsTrusteeship() {
        for (AbsRoomPos pos : this.getPosList()) {
            if (pos instanceof ZJHRoomPos) {
                ZJHRoomPos roomPos = (ZJHRoomPos) pos;
                
                // 如果是因积分不足而托管
                if (roomPos.isTrusteeship() && roomPos.isInsufficientPointsTrusteeship()) {
                    ZJHRoom room = (ZJHRoom) this.getRoom();
                    ZJHRoomSet roomSet = (ZJHRoomSet) room.getCurSet();
                    
                    // 如果当前有进行中的局，则检查积分是否已充足
                    if (roomSet != null && roomSet.state != SetState.End && roomSet.state != SetState.Init) {
                        // 检查积分是否已充足
                        Double currentPoints = roomPos.sportsPoint();
                        int requiredPoints = room.m_bottomPoint; // 使用房间底分作为所需积分
                        
                        if (currentPoints != null && currentPoints >= requiredPoints) {
                            // 积分已充足，取消托管
                            roomPos.setInsufficientPointsTrusteeship(false);
                            
                            // 通知所有玩家该位置的托管状态已解除
                            SZJH_Trusteeship msg = SZJH_Trusteeship.make(
                                room.getRoomID(),
                                roomPos.getPid(), 
                                roomPos.getPosID(), 
                                false
                            );
                            
                            // 添加到回放记录
                            if (roomSet != null) {
                                roomSet.getRoomPlayBack().addPlaybackList(msg, null);
                            }
                            
                            // 发送给所有玩家
                            this.getAllPosList().forEach(key -> {
                                if (Objects.nonNull(key) && !key.isRobot() && key.getPid() > 0L) {
                                    key.getPlayer().pushProto(msg);
                                }
                            });
                        }
                    }
                }
            }
        }
    }
}

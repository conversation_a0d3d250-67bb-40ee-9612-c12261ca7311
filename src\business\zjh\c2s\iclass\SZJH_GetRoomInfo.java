package business.zjh.c2s.iclass;

import business.global.pk.zjh.ZJHRoomSet;
import business.global.room.base.AbsRoomPos;
import business.zjh.c2s.cclass.ZJHRoom_Cfg;
import cenum.PrizeType;
import cenum.room.RoomState;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.room.Room_Dissolve;

import java.util.List;

/*
 * 用户信息
 * <AUTHOR>
 * */
public class SZJH_GetRoomInfo extends BaseSendMsg {

    public long roomID;
    public String key;
    public int createSec;
    public PrizeType prizeType;
    public RoomState state;
    public int setID;
    public long ownerID;
    public ZJHRoom_Cfg cfg;
    public ZJHRoomSet set;
    public List<AbsRoomPos> posList;
    public Room_Dissolve dissolve;
    public long createID;
	//public List<ArrayList<PlayerResult>> playerResult;


    public static SZJH_GetRoomInfo make(long roomID, String key, int createSec, PrizeType prizeType,
                                        RoomState state, int setID, long ownerID, ZJHRoom_Cfg cfg, ZJHRoomSet set, List<AbsRoomPos> posList,
                                        Room_Dissolve dissolve, long createID/*,List<ArrayList<PlayerResult>> playerResult*/) {
        SZJH_GetRoomInfo ret = new SZJH_GetRoomInfo();
        ret.roomID = roomID;
        ret.key = key;
        ret.createSec = createSec;
        ret.prizeType = prizeType;
        ret.state = state;
        ret.setID = setID;
        ret.ownerID = ownerID;
        ret.cfg = cfg;
        ret.set = set;
        ret.posList = posList;
        ret.dissolve = dissolve;
        ret.createID = createID;
        //ret.gameType = gameType;
        //ret.playerResult = playerResult;
        return ret;
    }
}
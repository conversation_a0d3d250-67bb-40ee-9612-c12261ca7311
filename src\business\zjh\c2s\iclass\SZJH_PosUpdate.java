package business.zjh.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.room.RoomPosInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 位置更新通知
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
@Data
@EqualsAndHashCode(callSuper = true)
public class SZJH_PosUpdate extends BaseSendMsg {
    // 房间ID
    private long roomID;
    // 玩家位置
    private int pos;
    //观众位置  -1表示没有观众
    private int visit;
    // 房间玩家信息
    private RoomPosInfo posInfo;
    // 房间观众信息
    private RoomPosInfo visitInfo;
    // 自定义数据
    private int custom;

    public static SZJH_PosUpdate make(long roomID, int pos, int visit, RoomPosInfo posInfo, RoomPosInfo visitInfo, int custom) {
        SZJH_PosUpdate ret = new SZJH_PosUpdate();
        ret.setRoomID(roomID);
        ret.setPos(pos);
        ret.setVisit(visit);
        ret.setPosInfo(posInfo);
        ret.setVisitInfo(visitInfo);
        ret.setCustom(custom);
        return ret;
    }

}
